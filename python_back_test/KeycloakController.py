from keycloak import KeycloakOpenID
from keycloak.exceptions import KeycloakError, KeycloakAuthenticationError, KeycloakConnectionError
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class KeycloakController:
    
    @staticmethod
    def keycloak_login(server_url, realm, client_id, username, password, client_secret=None):
        """
        Autentica un usuario en Keycloak y retorna información del token
        
        Args:
            server_url (str): URL del servidor Keycloak
            realm (str): ID del realm
            client_id (str): ID del cliente
            username (str): Nombre de usuario
            password (str): Contraseña del usuario
            client_secret (str, optional): Secret del cliente (para clientes confidenciales)
            
        Returns:
            dict: Respuesta con código, mensaje, resultado y status
        """
        try:
            # Asegurar que la URL termine con /
            if not server_url.endswith('/'):
                server_url += '/'
                
            # La URL base de Keycloak no necesita el /auth/realms/{realm}
            # Ya que KeycloakOpenID se encarga de construir la URL correcta
            logger.info(f"Intentando conectar a: {server_url}")
            
            # Inicializar el cliente de Keycloak con verificación SSL deshabilitada
            keycloak_openid = KeycloakOpenID(
                server_url=server_url,
                realm_name=realm,
                client_id=client_id,
                client_secret_key=client_secret,
                verify=False  # Deshabilitar verificación SSL para facilitar la depuración
            )
            
            logger.info(f"Intentando obtener token para cliente: {client_id}")
            
            # Intentar obtener el token
            token = keycloak_openid.token(username, password)
            
            logger.info("Token obtenido exitosamente")
            
            # Obtener información del token
            token_info = keycloak_openid.introspect(token['access_token'])
            
            # Obtener información del usuario
            user_info = keycloak_openid.userinfo(token['access_token'])
            
            return {
                "code": 200,
                "message": "success",
                "result": {
                    "token": token,
                    "token_info": token_info,
                    "user_info": user_info,
                    "connection_info": {
                        "server_url": server_url,
                        "realm": realm,
                        "client_id": client_id,
                        "username": username
                    }
                },
                "status": True
            }
            
            logger.info("Autenticación exitosa completada")
            
        except KeycloakAuthenticationError as e:
            logger.error(f"Error de autenticación: {e}")
            return {
                "code": 401,
                "message": "authentication_error",
                "result": str(e),
                "status": False
            }
            
        except KeycloakConnectionError as e:
            logger.error(f"Error de conexión: {e}")
            return {
                "code": 503,
                "message": "connection_error",
                "result": str(e),
                "status": False
            }
            
        except KeycloakError as e:
            logger.error(f"Error de Keycloak: {e}")
            return {
                "code": 400,
                "message": "keycloak_error",
                "result": str(e),
                "status": False
            }
            
        except Exception as e:
            logger.error(f"Error inesperado: {e}")
            return {
                "code": 500,
                "message": "internal_error",
                "result": str(e),
                "status": False
            }
    
    @staticmethod
    def validate_token(server_url, realm, client_id, access_token, client_secret=None):
        """
        Valida un token de acceso existente
        
        Args:
            server_url (str): URL del servidor Keycloak
            realm (str): Nombre del realm
            client_id (str): ID del cliente
            access_token (str): Token de acceso a validar
            client_secret (str, optional): Secret del cliente
            
        Returns:
            dict: Respuesta con información de validación
        """
        try:
            if not server_url.endswith('/'):
                server_url += '/'
                
            keycloak_openid = KeycloakOpenID(
                server_url=server_url,
                realm_name=realm,
                client_id=client_id,
                client_secret_key=client_secret,
                verify=True
            )
            
            # Introspeccionar el token
            token_info = keycloak_openid.introspect(access_token)
            
            if not token_info.get('active', False):
                return {
                    "code": 401,
                    "message": "token_invalid",
                    "result": "Token no está activo",
                    "status": False
                }
            
            # Obtener información del usuario si el token es válido
            try:
                user_info = keycloak_openid.userinfo(access_token)
            except Exception:
                user_info = None
            
            return {
                "code": 200,
                "message": "success",
                "result": {
                    "token_info": token_info,
                    "user_info": user_info,
                    "is_valid": True
                },
                "status": True
            }
            
        except Exception as e:
            logger.error(f"Error validando token: {e}")
            return {
                "code": 500,
                "message": "validation_error",
                "result": f"Error validando token: {str(e)}",
                "status": False
            }

    @staticmethod
    def health():
        """
        Endpoint de salud para verificar que el servicio está funcionando
        
        Returns:
            dict: Respuesta con código, mensaje, resultado y status
        """
        try:
            # Obtener información del sistema
            import platform
            import psutil
            
            memory = psutil.virtual_memory()
            
            return {
                "code": 200,
                "message": "success",
                "result": {
                    "service_status": "running",
                    "system_info": {
                        "python_version": platform.python_version(),
                        "system": platform.system(),
                        "architecture": platform.architecture()[0],
                        "memory": {
                            "total": memory.total,
                            "available": memory.available,
                            "percent": memory.percent
                        }
                    }
                },
                "status": True
            }
            
        except Exception as e:
            logger.error(f"Error en endpoint de salud: {e}")
            return {
                "code": 500,
                "message": "health_check_error",
                "result": str(e),
                "status": False
            }

# Función de conveniencia para mantener compatibilidad
def keycloakLogin(server_url, realm, client_id, username, password, client_secret=None):
    """Función de conveniencia para mantener compatibilidad con código existente"""
    return KeycloakController.keycloak_login(server_url, realm, client_id, username, password, client_secret)