from KeycloakController import KeycloakController
import logging
import os

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def main():
    # Configuration parameters
    # Configuración para Keycloak QA
    server_url = "https://keycloak-qa.infositio.dev/"
    realm = "master"
    client_id = "test"
    username = "user-dart"
    password = "1234"
    client_secret = "v3cXtEUhsqRmWmyvxO9SO1hQrCkLYMQV"
    
    # Configure environment variables for Keycloak
    os.environ['KEYCLOAK_SERVER_URL'] = server_url
    os.environ['KEYCLOAK_REALM'] = realm
    os.environ['KEYCLOAK_CLIENT_ID'] = client_id
    os.environ['KEYCLOAK_USERNAME'] = username
    os.environ['KEYCLOAK_PASSWORD'] = password
    os.environ['KEYCLOAK_CLIENT_SECRET'] = client_secret
    
    # Initialize Keycloak connection
    logger.info("Initializing Keycloak connection...")
    logger.info(f"Server URL: {server_url}")
    logger.info(f"Realm: {realm}")
    logger.info(f"Client ID: {client_id}")
    logger.info(f"Using client secret: {client_secret[:10]}... (truncated)")
    
    try:
        # Load credentials
        logger.info("Loading credentials...")
        
        # Call the login method
        result = KeycloakController.keycloak_login(
            server_url=server_url,
            realm=realm,
            client_id=client_id,
            username=username,
            password=password,
            client_secret=client_secret
        )
        
        # Print result
        logger.info("Login result:")
        logger.info(f"Status: {result['status']}")
        logger.info(f"Message: {result['message']}")
        
        if result['status']:
            logger.info("Login successful!")
            logger.info(f"Access Token: {result['result']['token']['access_token'][:10]}... (truncated)")
            logger.info(f"Refresh Token: {result['result']['token']['refresh_token'][:10]}... (truncated)")
            logger.info(f"Expires In: {result['result']['token']['expires_in']} seconds")
            logger.info(f"Token Type: {result['result']['token']['token_type']}")
            logger.info("User Info:")
            logger.info(f"Username: {result['result']['user_info'].get('preferred_username', 'Not available')}")
            logger.info(f"Email: {result['result']['user_info'].get('email', 'Not available')}")
            logger.info("Connection Info:")
            logger.info(f"Server URL: {result['result']['connection_info']['server_url']}")
            logger.info(f"Realm: {result['result']['connection_info']['realm']}")
            logger.info(f"Client ID: {result['result']['connection_info']['client_id']}")
            logger.info(f"Username: {result['result']['connection_info']['username']}")
        else:
            logger.error("Login failed!")
            logger.error(f"Error: {result['result']}")
            
    except Exception as e:
        logger.error(f"An error occurred: {str(e)}")
    
    # Exit program
    logger.info("Program execution completed.")
    exit(0)

if __name__ == "__main__":
    main()