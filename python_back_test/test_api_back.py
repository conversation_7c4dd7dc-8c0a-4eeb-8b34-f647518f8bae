from flask import Flask, jsonify, request
from KeycloakController import KeycloakController
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)

@app.route('/login', methods=['POST'])
def login():
    """
    Endpoint para autenticación de usuarios en Keycloak
    
    Body JSON esperado:
    {
        "server_url": "https://tu-keycloak.com/",
        "realm": "tu-realm",
        "client_id": "tu-client-id",
        "username": "usuario",
        "password": "contraseña",
        "client_secret": "secret" (opcional, solo para clientes confidenciales)
    }
    """
    try:
        # Obtener parámetros del request
        data = request.get_json()
        
        if not data:
            return jsonify({
                "code": 400,
                "message": "error",
                "result": "Body JSON requerido",
                "status": False
            }), 400
        
        # Llamar a la función de login
        result = KeycloakController.keycloak_login(
            server_url=data['server_url'],
            realm=data['realm'],
            client_id=data['client_id'],
            username=data['username'],
            password=data['password'],
            client_secret=data.get('client_secret')
        )
        
        status_code = result.get('code', 500)
        return jsonify(result), status_code
        
    except Exception as e:
        logger.error(f"Error inesperado en login: {e}")
        return jsonify({
            "code": 500,
            "message": "internal_error",
            "result": f"Error interno: {str(e)}",
            "status": False
        }), 500

@app.route('/validate-token', methods=['POST'])
def validate_token():
    """
    Endpoint para validar un token de acceso
    
    Body JSON esperado:
    {
        "server_url": "https://tu-keycloak.com/",
        "realm": "tu-realm",
        "client_id": "tu-client-id",
        "access_token": "token-a-validar",
        "client_secret": "secret" (opcional, solo para clientes confidenciales)
    }
    """
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                "code": 400,
                "message": "error",
                "result": "Body JSON requerido",
                "status": False
            }), 400
            
        result = KeycloakController.validate_token(
            server_url=data['server_url'],
            realm=data['realm'],
            client_id=data['client_id'],
            access_token=data['access_token'],
            client_secret=data.get('client_secret')
        )
        
        status_code = result.get('code', 500)
        return jsonify(result), status_code
        
    except Exception as e:
        logger.error(f"Error validando token: {e}")
        return jsonify({
            "code": 500,
            "message": "validation_error",
            "result": f"Error validando token: {str(e)}",
            "status": False
        }), 500

@app.route('/health', methods=['GET'])
def health():
    """
    Endpoint de salud para verificar que el servicio está funcionando
    """
    try:
        result = KeycloakController.health()
        status_code = result.get('code', 500)
        return jsonify(result), status_code
        
    except Exception as e:
        logger.error(f"Error en endpoint de salud: {e}")
        return jsonify({
            "code": 500,
            "message": "health_check_error",
            "result": f"Error en endpoint de salud: {str(e)}",
            "status": False
        }), 500

@app.errorhandler(404)
def not_found(error):
    return jsonify({
        "code": 404,
        "message": "not_found",
        "result": "Endpoint no encontrado",
        "status": False
    }), 404

@app.errorhandler(405)
def method_not_allowed(error):
    return jsonify({
        "code": 405,
        "message": "method_not_allowed",
        "result": "Método HTTP no permitido",
        "status": False
    }), 405

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)