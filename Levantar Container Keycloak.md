# Pasos a seguir para instalar Keycloak con Docker

0. Instalar Docker y Docker Compose desde `https://www.docker.com/products/docker-desktop/`.
    - Comprobar versiones: `docker --version`.
    - Comprobar versiones: `docker compose version`.
    
1. <PERSON>rear una red Docker (para comunicar los contenedores). Esto permite que Keycloak y PostgreSQL se comuniquen internamente sin exponer todos los puertos.

    ```bash
    docker network create keycloak-network
    ```

    Resultado en mi caso: `b64aaa8c012583ce8e9565e05e290abacd91abe2d98a780a4767f98f6e8f9b08`.

2. Crear un archivo `docker-compose.yml` en una carpeta vacía:

    ```yaml
    services:
    keycloak:
        image: quay.io/keycloak/keycloak:24.0.4
        container_name: keycloak
        ports:
        - "8086:8080"
        environment:
        KEYCLOAK_ADMIN: admin
        <PERSON>_ADMIN_PASSWORD: admin_password
        KC_DB: postgres
        KC_DB_URL: **************************************************************
        KC_DB_URL_PORT: 5432
        KC_DB_USERNAME: postgres
        KC_DB_PASSWORD: Ko131an4E6ov096T
        KC_HOSTNAME: keycloak-qa.infositio.dev
        KC_PROXY: edge
        volumes:
        - keycloak_data:/opt/keycloak/data
        command: start-dev
        networks:
        - cloudflared_cloudflared_network

    volumes:
    postgres_data:
    keycloak_data:

    networks:
    cloudflared_cloudflared_network:
        external: true
    ```

3. Ejecutar los contenedores. Desde el mismo directorio donde está el `docker-compose.yml`:

    ```bash
    docker compose up -d
    ```

4. Acceder al panel de administración de Keycloak: `http://localhost:8086/admin`.

    - **Usuario**: admin
    - **Contraseña**: admin_password

5. Comandos de ayuda:
    - Comprobar si hay fallas: `docker logs -f keycloak`.
    - Detener servicios: `docker compose down`.
    - Actualizar imágenes y reiniciar: `docker compose pull && docker compose up -d`.



## Fallas comunes

### Keycloak espera HTTPS y no lo estás configurando

- Buscar la linea "*command: start --optimized*" en el ***.yml*** y cambiarla por "*command: start-dev*".
- Reiniciar contenedores: 
    - `docker compose down`
    - `docker compose up -d`


# Comandos para Ejecutar Keycloak

Una vez instalado Keycloak, puedes usar los siguientes comandos para administrar su ejecución:

- **Ejecutar Keycloak**: `docker compose up -d` (estar en la ruta donde esta el archivo).
- **Detener Keycloak**: `docker compose down`.
- **Reiniciar Keycloak**: `docker compose down && docker compose up -d`.


# Configuración dentro de Keycloak

1. En Keycloak, un *realm* (reino) es como una caja independiente donde viven los usuarios, roles y configuraciones de seguridad. 
    - **Crear un Realm**: Ir a la `menú desplegable superior izquierdo → "Create realm"`.
    - **Nomenclatura sugerida**: "`nombre_de_entidad_o_proyecto` - `propósito` - `entorno`". Ejemplo: "*saesa-dart-dev*" 
2. Crear un Cliente: 
    - `Menú izquierdo → Clients → Create clients`
        - **Client ID**: dart-api
        - **Client Protocol**: openid-connect
        - **Access Type**: confidential
        - **Habilita**: Standard Flow Enabled y Service Accounts Enabled
        - **Valid Redirect URIs**: `http://localhost:<PUERTO_API>/*`
        - **Web Origins**: `http://localhost:<PUERTO_API>`
3. Crear Roles
    - `Menú izquierdo → Roles`
    - **Crear roles como**: api-user, admin
4. Crear un Usuario
    - `Menú izquierdo → Users → Add user`
        - **Username**: user1
    - Ir a `“Credentials” → asignar contraseña`
    - Ir a `“Role Mappings” → asignar roles creados`
5. Configurar expiración de tokens (opcional)
    - `Menú izquierdo → Realm Settings → Tokens`
    - **Access Token Lifespan**: 5 minutes
    - **Refresh Token Lifespan**: 60 minutes
