# Keycloak Flutter Demo

Esta aplicación Flutter demuestra la integración con Keycloak utilizando un servicio personalizado que replica la funcionalidad del `KeycloakController` de Python.

## Características

La aplicación incluye tres funcionalidades principales:

1. **LOGIN** - Autentica un usuario en Keycloak y obtiene tokens de acceso
2. **VALIDAR TOKEN** - Valida un token de acceso existente
3. **HEALTH** - Verifica el estado del servicio

## Arquitectura

### KeycloakController (Dart)

El archivo `lib/services/keycloak_service.dart` contiene la clase `KeycloakController` que replica exactamente la funcionalidad del controlador Python:

- **keycloak_login()** - Método estático para autenticación
- **validate_token()** - Método estático para validación de tokens
- **health()** - Método estático para verificación de salud del servicio

### Nombres de Variables y Métodos

Se mantienen los mismos nombres que en el código Python:
- `server_url` (en lugar de `serverUrl`)
- `client_id` (en lugar de `clientId`)
- `client_secret` (en lugar de `clientSecret`)
- `access_token` (en lugar de `accessToken`)
- `keycloak_login` (en lugar de `keycloakLogin`)
- `validate_token` (en lugar de `validateToken`)

### Estructura de Respuesta

Todas las respuestas siguen el mismo formato que el código Python:

```dart
{
  "code": 200,
  "message": "success",
  "result": {
    // Datos específicos de la operación
  },
  "status": true
}
```

## Configuración

### Dependencias

El proyecto utiliza las siguientes dependencias:

```yaml
dependencies:
  dio: ^5.4.0      # Cliente HTTP para requests a Keycloak
  logging: ^1.2.0  # Framework de logging
```

### Valores por Defecto

La aplicación viene preconfigurada con valores típicos de Keycloak:

- **Server URL**: `http://localhost:8080/`
- **Realm**: `master`
- **Client ID**: `admin-cli`
- **Username**: `admin`
- **Password**: `admin`

## Uso

### 1. Instalación

```bash
# Instalar dependencias
flutter pub get

# Ejecutar la aplicación
flutter run
```

### 2. Configuración de Keycloak

1. Asegúrate de tener Keycloak ejecutándose en `http://localhost:8080`
2. Configura los campos en la aplicación según tu instalación de Keycloak
3. Para clientes públicos, deja el campo "Client Secret" vacío

### 3. Operaciones

#### Login
1. Completa los campos de configuración
2. Presiona el botón **LOGIN**
3. Si es exitoso, el token de acceso se copiará automáticamente al campo correspondiente

#### Validar Token
1. Asegúrate de tener un token en el campo "Access Token"
2. Presiona el botón **VALIDAR TOKEN**
3. El resultado mostrará si el token es válido y la información del usuario

#### Health Check
1. Presiona el botón **HEALTH**
2. Verifica que el servicio esté funcionando correctamente

## Manejo de Errores

El servicio maneja diferentes tipos de errores:

- **401** - Error de autenticación (credenciales incorrectas)
- **503** - Error de conexión (Keycloak no disponible)
- **400** - Error de Keycloak (configuración incorrecta)
- **500** - Error interno del servicio

## Logging

La aplicación incluye logging detallado que se muestra en la consola:

```dart
Logger.root.level = Level.ALL;
Logger.root.onRecord.listen((record) {
  print('${record.level.name}: ${record.time}: ${record.message}');
});
```

## Comparación con Python

### Similitudes

- Mismos nombres de métodos y variables
- Misma estructura de respuesta
- Mismo manejo de errores
- Misma funcionalidad de introspección de tokens

### Diferencias

- Uso de `Future<Map<String, dynamic>>` en lugar de `dict`
- Manejo asíncrono con `async/await`
- Cliente HTTP Dio en lugar de la librería `keycloak`
- Logging con el paquete `logging` de Dart

## Estructura del Proyecto

```
dart_test/
├── lib/
│   ├── main.dart                    # Aplicación Flutter principal
│   └── services/
│       └── keycloak_service.dart    # Servicio de Keycloak
├── pubspec.yaml                     # Dependencias del proyecto
└── README.md                        # Este archivo
```

## Notas Técnicas

- El servicio utiliza métodos estáticos para mantener compatibilidad con el patrón del código Python
- Se incluye una función de conveniencia `keycloakLogin()` para mantener compatibilidad
- El cliente HTTP está configurado con timeouts de 30 segundos
- SSL verification está deshabilitada para facilitar desarrollo local
- Los interceptores de Dio proporcionan logging automático de requests/responses

## Troubleshooting

### Error de conexión
- Verifica que Keycloak esté ejecutándose
- Confirma la URL del servidor
- Revisa la configuración de red/firewall

### Error de autenticación
- Verifica las credenciales
- Confirma el realm y client ID
- Revisa la configuración del cliente en Keycloak

### Token inválido
- El token puede haber expirado
- Realiza un nuevo login para obtener un token fresco
- Verifica que el token corresponda al realm correcto
