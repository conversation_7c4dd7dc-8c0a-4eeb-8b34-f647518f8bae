import 'package:flutter/material.dart';
import 'package:logging/logging.dart';
import 'services/keycloak_service.dart';

void main() {
  // Configurar logging
  Logger.root.level = Level.ALL;
  Logger.root.onRecord.listen((record) {
    print('${record.level.name}: ${record.time}: ${record.message}');
  });

  // Inicializar el servicio de Keycloak
  KeycloakService.initialize();

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Keycloak Flutter Demo',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
        useMaterial3: true,
      ),
      home: const KeycloakDemoPage(title: 'Keycloak Flutter Demo'),
    );
  }
}

class KeycloakDemoPage extends StatefulWidget {
  const KeycloakDemoPage({super.key, required this.title});

  final String title;

  @override
  State<KeycloakDemoPage> createState() => _KeycloakDemoPageState();
}

class _KeycloakDemoPageState extends State<KeycloakDemoPage> {
  // Controladores para los campos de texto
  final TextEditingController _serverUrlController = TextEditingController();
  final TextEditingController _realmController = TextEditingController();
  final TextEditingController _clientIdController = TextEditingController();
  final TextEditingController _usernameController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _clientSecretController = TextEditingController();
  final TextEditingController _accessTokenController = TextEditingController();

  // Estado de la aplicación
  String _result = '';
  bool _isLoading = false;
  String? _currentAccessToken;

  @override
  void initState() {
    super.initState();
    // Valores por defecto usando las constantes de Keycloak
    _serverUrlController.text = KeycloakConstants.DEFAULT_SERVER_URL;
    _realmController.text = KeycloakConstants.DEFAULT_REALM;
    _clientIdController.text = KeycloakConstants.DEFAULT_CLIENT_ID;
    _usernameController.text = KeycloakConstants.DEFAULT_USERNAME;
    _passwordController.text = KeycloakConstants.DEFAULT_PASSWORD;
    _clientSecretController.text = KeycloakConstants.DEFAULT_CLIENT_SECRET;
  }

  @override
  void dispose() {
    _serverUrlController.dispose();
    _realmController.dispose();
    _clientIdController.dispose();
    _usernameController.dispose();
    _passwordController.dispose();
    _clientSecretController.dispose();
    _accessTokenController.dispose();
    super.dispose();
  }

  Future<void> _performLogin() async {
    setState(() {
      _isLoading = true;
      _result = 'Realizando login...';
    });

    try {
      final response = await KeycloakService.keycloak_login(
        server_url: _serverUrlController.text,
        realm: _realmController.text,
        client_id: _clientIdController.text,
        username: _usernameController.text,
        password: _passwordController.text,
        client_secret: _clientSecretController.text.isEmpty
            ? null
            : _clientSecretController.text,
      );

      setState(() {
        _result = _formatResponse(response);
        if (response['status'] == true && response['result'] != null) {
          final token = response['result']['token'];
          if (token != null && token['access_token'] != null) {
            _currentAccessToken = token['access_token'];
            _accessTokenController.text = _currentAccessToken!;
          }
        }
      });
    } catch (e) {
      setState(() {
        _result = 'Error: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _validateToken() async {
    if (_accessTokenController.text.isEmpty) {
      setState(() {
        _result = 'Error: No hay token para validar. Realiza login primero.';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _result = 'Validando token...';
    });

    try {
      final response = await KeycloakService.validate_token(
        server_url: _serverUrlController.text,
        realm: _realmController.text,
        client_id: _clientIdController.text,
        access_token: _accessTokenController.text,
        client_secret: _clientSecretController.text.isEmpty
            ? null
            : _clientSecretController.text,
      );

      setState(() {
        _result = _formatResponse(response);
      });
    } catch (e) {
      setState(() {
        _result = 'Error: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _checkHealth() async {
    setState(() {
      _isLoading = true;
      _result = 'Verificando salud del servicio...';
    });

    try {
      final response = await KeycloakService.health();

      setState(() {
        _result = _formatResponse(response);
      });
    } catch (e) {
      setState(() {
        _result = 'Error: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  String _formatResponse(Map<String, dynamic> response) {
    final buffer = StringBuffer();
    buffer.writeln('=== RESPUESTA ===');
    buffer.writeln('Código: ${response['code']}');
    buffer.writeln('Mensaje: ${response['message']}');
    buffer.writeln('Status: ${response['status']}');
    buffer.writeln('');

    if (response['result'] != null) {
      buffer.writeln('=== RESULTADO ===');
      _formatObject(response['result'], buffer, 0);
    }

    return buffer.toString();
  }

  void _formatObject(dynamic obj, StringBuffer buffer, int indent) {
    final indentStr = '  ' * indent;

    if (obj is Map<String, dynamic>) {
      obj.forEach((key, value) {
        buffer.write('$indentStr$key: ');
        if (value is Map || value is List) {
          buffer.writeln('');
          _formatObject(value, buffer, indent + 1);
        } else {
          buffer.writeln(value?.toString() ?? 'null');
        }
      });
    } else if (obj is List) {
      for (int i = 0; i < obj.length; i++) {
        buffer.writeln('$indentStr[$i]:');
        _formatObject(obj[i], buffer, indent + 1);
      }
    } else {
      buffer.writeln('$indentStr${obj?.toString() ?? 'null'}');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        title: Text(widget.title),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Expanded(
              flex: 1,
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    const Text(
                      'Configuración de Keycloak',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: _serverUrlController,
                      decoration: const InputDecoration(
                        labelText: 'Server URL',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextField(
                      controller: _realmController,
                      decoration: const InputDecoration(
                        labelText: 'Realm',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextField(
                      controller: _clientIdController,
                      decoration: const InputDecoration(
                        labelText: 'Client ID',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextField(
                      controller: _usernameController,
                      decoration: const InputDecoration(
                        labelText: 'Username',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextField(
                      controller: _passwordController,
                      decoration: const InputDecoration(
                        labelText: 'Password',
                        border: OutlineInputBorder(),
                      ),
                      obscureText: true,
                    ),
                    const SizedBox(height: 8),
                    TextField(
                      controller: _clientSecretController,
                      decoration: const InputDecoration(
                        labelText: 'Client Secret (opcional)',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextField(
                      controller: _accessTokenController,
                      decoration: const InputDecoration(
                        labelText: 'Access Token (para validación)',
                        border: OutlineInputBorder(),
                      ),
                      maxLines: 3,
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton(
                            onPressed: _isLoading ? null : _performLogin,
                            child: const Text('LOGIN'),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: _isLoading ? null : _validateToken,
                            child: const Text('VALIDAR TOKEN'),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: _isLoading ? null : _checkHealth,
                            child: const Text('HEALTH'),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Expanded(
              flex: 1,
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Resultado:',
                      style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Expanded(
                      child: SingleChildScrollView(
                        child: _isLoading
                            ? const Center(child: CircularProgressIndicator())
                            : Text(
                                _result.isEmpty ? 'Presiona un botón para ver el resultado' : _result,
                                style: const TextStyle(fontFamily: 'monospace'),
                              ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
