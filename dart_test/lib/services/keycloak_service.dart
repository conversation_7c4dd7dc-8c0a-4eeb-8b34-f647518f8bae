import 'dart:convert';
import 'dart:io';
import 'package:dio/dio.dart';
import 'package:logging/logging.dart';
import 'package:flutter/foundation.dart';

// Clases de excepciones
abstract class KeycloakException implements Exception {
  final String message;
  final int? statusCode;

  KeycloakException(this.message, [this.statusCode]);

  @override
  String toString() => message;
}

class KeycloakConnectionException extends KeycloakException {
  KeycloakConnectionException(String message, [int? statusCode])
      : super(message, statusCode);
}

class KeycloakAuthenticationException extends KeycloakException {
  KeycloakAuthenticationException(String message, [int? statusCode])
      : super(message, statusCode);
}

// Servicio Keycloak
class KeycloakService {
  // Singleton pattern
  static final KeycloakService _instance = KeycloakService._internal();
  factory KeycloakService() => _instance;
  KeycloakService._internal();

  // Logger
  static final Logger _logger = Logger('KeycloakService');
  
  // Cliente HTTP
  late final Dio _dio;

  /// Inicializa el servicio
  void initialize() {
    _dio = Dio(BaseOptions(
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json',
      },
    ));

    // Configurar interceptores
    _dio.interceptors.add(LogInterceptor(
      request: true,
      requestBody: false,
      response: true,
      responseBody: false,
      error: true,
    ));

    _setupLogging();
  }

  void _setupLogging() {
    Logger.root.level = Level.INFO;
    Logger.root.onRecord.listen((record) {
      debugPrint('${record.level.name}: ${record.time}: ${record.message}');
    });
  }

  String _normalizeUrl(String url) {
    if (!url.endsWith('/')) url += '/';
    return url;
  }

  Future<Map<String, dynamic>> _introspectToken({
    required String server_url,
    required String realm,
    required String client_id,
    String? client_secret,
    required String token,
  }) async {
    final formData = {
      'token': token,
      'client_id': client_id,
    };

    if (client_secret != null && client_secret.isNotEmpty) {
      formData['client_secret'] = client_secret;
    }

    final response = await _dio.post(
      '${server_url}auth/realms/$realm/protocol/openid-connect/token/introspect',
      data: formData,
    );
    return response.data as Map<String, dynamic>;
  }

  Future<Map<String, dynamic>> _getUserInfo({
    required String server_url,
    required String realm,
    required String access_token,
  }) async {
    final response = await _dio.get(
      '${server_url}auth/realms/$realm/protocol/openid-connect/userinfo',
      options: Options(
        headers: {'Authorization': 'Bearer $access_token'},
      ),
    );
    return response.data as Map<String, dynamic>;
  }

  /// Autentica un usuario en Keycloak
  Future<Map<String, dynamic>> keycloak_login({
    required String server_url,
    required String realm,
    required String client_id,
    required String username,
    required String password,
    String? client_secret,
  }) async {
    try {
      // Normalizar URL
      final normalizedUrl = _normalizeUrl(server_url);
      
      _logger.info('Intentando conectar a: $normalizedUrl');
      
      // Preparar datos del formulario
      final formData = {
        'grant_type': 'password',
        'client_id': client_id,
        'username': username,
        'password': password,
      };

      if (client_secret != null && client_secret.isNotEmpty) {
        formData['client_secret'] = client_secret;
      }

      // Realizar petición
      final response = await _dio.post(
        '${normalizedUrl}auth/realms/$realm/protocol/openid-connect/token',
        data: formData,
      );

      if (response.statusCode != 200) {
        throw KeycloakAuthenticationException(
          'Error de autenticación', 
          response.statusCode,
        );
      }

      _logger.info('Token obtenido exitosamente');

      // Obtener información del token
      final tokenInfo = await _introspectToken(
        server_url: normalizedUrl,
        realm: realm,
        client_id: client_id,
        client_secret: client_secret,
        token: response.data['access_token'] as String,
      );

      // Obtener información del usuario
      final userInfo = await _getUserInfo(
        server_url: normalizedUrl,
        realm: realm,
        access_token: response.data['access_token'] as String,
      );

      return {
        'code': 200,
        'message': 'success',
        'result': {
          'token': response.data,
          'token_info': tokenInfo,
          'user_info': userInfo,
          'connection_info': {
            'server_url': normalizedUrl,
            'realm': realm,
            'client_id': client_id,
            'username': username,
          },
        },
        'status': true,
      };

    } catch (e) {
      _logger.error('Error inesperado: $e');
      return {
        'code': 500,
        'message': 'internal_error',
        'result': e.toString(),
        'status': false,
      };
    }
  }

  /// Valida un token de acceso existente
  Future<Map<String, dynamic>> validate_token({
    required String server_url,
    required String realm,
    required String client_id,
    required String access_token,
    String? client_secret,
  }) async {
    try {
      final normalizedUrl = _normalizeUrl(server_url);
      
      _logger.info('Validando token...');

      // Introspeccionar token
      final tokenInfo = await _introspectToken(
        server_url: normalizedUrl,
        realm: realm,
        client_id: client_id,
        client_secret: client_secret,
        token: access_token,
      );

      if (!tokenInfo['active'] as bool) {
        return {
          'code': 401,
          'message': 'token_invalid',
          'result': 'Token no está activo',
          'status': false,
        };
      }

      // Obtener información del usuario si el token es válido
      final userInfo = await _getUserInfo(
        server_url: normalizedUrl,
        realm: realm,
        access_token: access_token,
      );

      return {
        'code': 200,
        'message': 'success',
        'result': {
          'token_info': tokenInfo,
          'user_info': userInfo,
          'is_valid': true,
        },
        'status': true,
      };

    } catch (e) {
      _logger.error('Error validando token: $e');
      return {
        'code': 500,
        'message': 'validation_error',
        'result': 'Error validando token: $e',
        'status': false,
      };
    }
  }

  /// Endpoint de salud para verificar que el servicio está funcionando
  Future<Map<String, dynamic>> health() async {
    try {
      // Obtener información del sistema
      final platform = Platform.operatingSystem;
      final architecture = Platform.numberOfProcessors.toString();
      final memory = ProcessInfo.currentRss;

      return {
        'code': 200,
        'message': 'success',
        'result': {
          'service_status': 'running',
          'system_info': {
            'platform': platform,
            'architecture': architecture,
            'memory': {
              'total': memory,
              'unit': 'bytes',
            },
          },
        },
        'status': true,
      };
    } catch (e) {
      _logger.error('Error en health check: $e');
      return {
        'code': 500,
        'message': 'internal_error',
        'result': 'Error en health check: $e',
        'status': false,
      };
    }
  }
}
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json',
      },
    ));

    // Configurar interceptores para logging
    _dio.interceptors.add(LogInterceptor(
      request: true,
      requestBody: false, // No logear credenciales
      response: true,
      responseBody: false,
      error: true,
    ));

    _setupLogging();
  }

  void _setupLogging() {
    Logger.root.level = Level.INFO;
    Logger.root.onRecord.listen((record) {
      debugPrint('${record.level.name}: ${record.time}: ${record.message}');
    });
  }

  String _normalizeUrl(String url) {
    if (!url.endsWith('/')) url += '/';
    return url;
  }

  Future<Map<String, dynamic>> _introspectToken({
    required String server_url,
    required String realm,
    required String client_id,
    String? client_secret,
    required String token,
  }) async {
    final formData = {
      'token': token,
      'client_id': client_id,
    };

    if (client_secret != null && client_secret.isNotEmpty) {
      formData['client_secret'] = client_secret;
    }

    final response = await _dio.post(
      '${server_url}auth/realms/$realm/protocol/openid-connect/token/introspect',
      data: formData,
    );
    return response.data as Map<String, dynamic>;
  }

  Future<Map<String, dynamic>> _getUserInfo({
    required String server_url,
    required String realm,
    required String access_token,
  }) async {
    final response = await _dio.get(
      '${server_url}auth/realms/$realm/protocol/openid-connect/userinfo',
      options: RequestOptions(
        headers: {'Authorization': 'Bearer $access_token'},
      ),
    );
    return response.data as Map<String, dynamic>;
  }

  /// Autentica un usuario en Keycloak
  Future<Map<String, dynamic>> keycloak_login({
    required String server_url,
    required String realm,
    required String client_id,
    required String username,
    required String password,
    String? client_secret,
  }) async {
    try {
      // Normalizar URL
      final normalizedUrl = _normalizeUrl(server_url);
      
      _logger.info('Intentando conectar a: $normalizedUrl');
      
      // Preparar datos del formulario
      final formData = {
        'grant_type': 'password',
        'client_id': client_id,
        'username': username,
        'password': password,
      };

      if (client_secret != null && client_secret.isNotEmpty) {
        formData['client_secret'] = client_secret;
      }

      // Realizar petición
      final response = await _dio.post(
        '${normalizedUrl}auth/realms/$realm/protocol/openid-connect/token',
        data: formData,
      );

      if (response.statusCode != 200) {
        throw KeycloakAuthenticationException(
          'Error de autenticación', 
          response.statusCode,
        );
      }

      _logger.info('Token obtenido exitosamente');

      // Obtener información del token
      final tokenInfo = await _introspectToken(
        server_url: normalizedUrl,
        realm: realm,
        client_id: client_id,
        client_secret: client_secret,
        token: response.data['access_token'] as String,
      );

      // Obtener información del usuario
      final userInfo = await _getUserInfo(
        server_url: normalizedUrl,
        realm: realm,
        access_token: response.data['access_token'] as String,
      );

      return {
        'code': 200,
        'message': 'success',
        'result': {
          'token': response.data,
          'token_info': tokenInfo,
          'user_info': userInfo,
          'connection_info': {
            'server_url': normalizedUrl,
            'realm': realm,
            'client_id': client_id,
            'username': username,
          },
        },
        'status': true,
      };

    } on KeycloakAuthenticationException catch (e) {
      _logger.error('Error de autenticación: $e');
      return {
        'code': 401,
        'message': 'authentication_error',
        'result': e.toString(),
        'status': false,
      };
    } on DioException catch (e) {
      _logger.error('Error de conexión: $e');
      return {
        'code': 503,
        'message': 'connection_error',
        'result': e.toString(),
        'status': false,
      };
    } catch (e) {
      _logger.error('Error inesperado: $e');
      return {
        'code': 500,
        'message': 'internal_error',
        'result': e.toString(),
        'status': false,
      };
    }
  }

  /// Valida un token de acceso existente
  Future<Map<String, dynamic>> validate_token({
    required String server_url,
    required String realm,
    required String client_id,
    required String access_token,
    String? client_secret,
  }) async {
    try {
      final normalizedUrl = _normalizeUrl(server_url);
      
      _logger.info('Validando token...');

      // Introspeccionar token
      final tokenInfo = await _introspectToken(
        server_url: normalizedUrl,
        realm: realm,
        client_id: client_id,
        client_secret: client_secret,
        token: access_token,
      );

      if (!tokenInfo['active'] as bool) {
        return {
          'code': 401,
          'message': 'token_invalid',
          'result': 'Token no está activo',
          'status': false,
        };
      }

      // Obtener información del usuario si el token es válido
      final userInfo = await _getUserInfo(
        server_url: normalizedUrl,
        realm: realm,
        access_token: access_token,
      );

      return {
        'code': 200,
        'message': 'success',
        'result': {
          'token_info': tokenInfo,
          'user_info': userInfo,
          'is_valid': true,
        },
        'status': true,
      };

    } catch (e) {
      _logger.error('Error validando token: $e');
      return {
        'code': 500,
        'message': 'validation_error',
        'result': 'Error validando token: $e',
        'status': false,
  /// 
  /// [serverUrl] - URL base del servidor Keycloak
  /// [realm] - Nombre del realm
  /// [clientId] - ID del cliente
  /// [refreshToken] - Refresh token para invalidar
  /// [clientSecret] - Secret del cliente (opcional)
  Future<bool> logout({
    required String serverUrl,
    required String realm,
    required String clientId,
    required String refreshToken,
    String? clientSecret,
  }) async {
    try {
      final normalizedUrl = _normalizeUrl(serverUrl);
      final logoutEndpoint = '${normalizedUrl}auth/realms/$realm/protocol/openid-connect/logout';

      final formData = <String, String>{
        'client_id': clientId,
        'refresh_token': refreshToken,
      };

      if (clientSecret != null && clientSecret.isNotEmpty) {
        formData['client_secret'] = clientSecret;
      }

      final response = await _dio.post(
        logoutEndpoint,
        data: formData,
      );

      return response.statusCode == 204;

    } catch (e) {
      _logger.severe('Error en logout: $e');
      return false;
    }
  }

  // MÉTODOS PRIVADOS

  String _normalizeUrl(String url) {
    return url.endsWith('/') ? url : '$url/';
  }

  Future<Map<String, dynamic>> _introspectToken({
    required String serverUrl,
    required String realm,
    required String clientId,
    String? clientSecret,
    required String token,
  }) async {
    final introspectEndpoint = '${serverUrl}auth/realms/$realm/protocol/openid-connect/token/introspect';

    final formData = <String, String>{
      'token': token,
      'client_id': clientId,
    };

    if (clientSecret != null && clientSecret.isNotEmpty) {
      formData['client_secret'] = clientSecret;
    }

    final response = await _dio.post(
      introspectEndpoint,
      data: formData,
    );

    if (response.statusCode != 200) {
      throw KeycloakException(
        'Error en introspección de token',
        response.statusCode,
      );
    }

    return response.data as Map<String, dynamic>;
  }

  Future<UserInfo?> _getUserInfo({
    required String serverUrl,
    required String realm,
    required String accessToken,
  }) async {
    final userInfoEndpoint = '${serverUrl}auth/realms/$realm/protocol/openid-connect/userinfo';

    try {
      final response = await _dio.get(
        userInfoEndpoint,
        options: Options(
          headers: {
            'Authorization': 'Bearer $accessToken',
          },
        ),
      );

      if (response.statusCode == 200) {
        return UserInfo.fromJson(response.data);
      }
    } catch (e) {
      _logger.warning('Error obteniendo información del usuario: $e');
    }

    return null;
  }
}