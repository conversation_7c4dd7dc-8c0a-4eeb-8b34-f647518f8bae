import 'dart:io';
import 'package:dio/dio.dart';
import 'package:logging/logging.dart';
import 'package:flutter/foundation.dart';

// Clases de excepciones personalizadas para Keycloak
abstract class KeycloakError implements Exception {
  final String message;
  final int? statusCode;

  KeycloakError(this.message, [this.statusCode]);

  @override
  String toString() => message;
}

class KeycloakAuthenticationError extends KeycloakError {
  KeycloakAuthenticationError(String message, [int? statusCode])
      : super(message, statusCode);
}

class KeycloakConnectionError extends KeycloakError {
  KeycloakConnectionError(String message, [int? statusCode])
      : super(message, statusCode);
}

/// Servicio de Keycloak que replica la funcionalidad del KeycloakController de Python
class KeycloakService {
  // Logger estático
  static final Logger logger = Logger('KeycloakService');

  // Cliente HTTP estático
  static late final Dio _dio;

  /// Inicializa el cliente HTTP y el logging
  static void initialize() {
    _dio = Dio(BaseOptions(
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json',
      },
    ));

    // Configurar interceptores para logging
    _dio.interceptors.add(LogInterceptor(
      request: true,
      requestBody: false, // No logear credenciales
      responseHeader: true,
      responseBody: false,
      error: true,
    ));

    _setupLogging();
  }

  static void _setupLogging() {
    Logger.root.level = Level.INFO;
    Logger.root.onRecord.listen((record) {
      debugPrint('${record.level.name}: ${record.time}: ${record.message}');
    });
  }

  /// Normaliza la URL asegurando que termine con /
  static String _normalizeUrl(String url) {
    if (!url.endsWith('/')) url += '/';
    return url;
  }

  /// Método privado para introspeccionar tokens
  static Future<Map<String, dynamic>> _introspectToken({
    required String server_url,
    required String realm,
    required String client_id,
    String? client_secret,
    required String token,
  }) async {
    final formData = <String, String>{
      'token': token,
      'client_id': client_id,
    };

    if (client_secret != null && client_secret.isNotEmpty) {
      formData['client_secret'] = client_secret;
    }

    final response = await _dio.post(
      '${server_url}auth/realms/$realm/protocol/openid-connect/token/introspect',
      data: formData,
    );
    return response.data as Map<String, dynamic>;
  }

  /// Método privado para obtener información del usuario
  static Future<Map<String, dynamic>> _getUserInfo({
    required String server_url,
    required String realm,
    required String access_token,
  }) async {
    final response = await _dio.get(
      '${server_url}auth/realms/$realm/protocol/openid-connect/userinfo',
      options: Options(
        headers: {'Authorization': 'Bearer $access_token'},
      ),
    );
    return response.data as Map<String, dynamic>;
  }

  /// Autentica un usuario en Keycloak y retorna información del token
  ///
  /// Args:
  ///   server_url (String): URL del servidor Keycloak
  ///   realm (String): ID del realm
  ///   client_id (String): ID del cliente
  ///   username (String): Nombre de usuario
  ///   password (String): Contraseña del usuario
  ///   client_secret (String, optional): Secret del cliente (para clientes confidenciales)
  ///
  /// Returns:
  ///   Map<String, dynamic>: Respuesta con código, mensaje, resultado y status
  static Future<Map<String, dynamic>> keycloak_login({
    required String server_url,
    required String realm,
    required String client_id,
    required String username,
    required String password,
    String? client_secret,
  }) async {
    try {
      // Asegurar que la URL termine con /
      String normalizedUrl = _normalizeUrl(server_url);

      // La URL base de Keycloak no necesita el /auth/realms/{realm}
      // Ya que KeycloakOpenID se encarga de construir la URL correcta
      logger.info('Intentando conectar a: $normalizedUrl');

      // Preparar datos del formulario
      final formData = <String, String>{
        'grant_type': 'password',
        'client_id': client_id,
        'username': username,
        'password': password,
      };

      if (client_secret != null && client_secret.isNotEmpty) {
        formData['client_secret'] = client_secret;
      }

      logger.info('Intentando obtener token para cliente: $client_id');

      // Intentar obtener el token
      final response = await _dio.post(
        '${normalizedUrl}auth/realms/$realm/protocol/openid-connect/token',
        data: formData,
      );

      logger.info('Token obtenido exitosamente');

      // Obtener información del token
      final tokenInfo = await _introspectToken(
        server_url: normalizedUrl,
        realm: realm,
        client_id: client_id,
        client_secret: client_secret,
        token: response.data['access_token'] as String,
      );

      // Obtener información del usuario
      final userInfo = await _getUserInfo(
        server_url: normalizedUrl,
        realm: realm,
        access_token: response.data['access_token'] as String,
      );

      logger.info('Autenticación exitosa completada');

      return {
        'code': 200,
        'message': 'success',
        'result': {
          'token': response.data,
          'token_info': tokenInfo,
          'user_info': userInfo,
          'connection_info': {
            'server_url': normalizedUrl,
            'realm': realm,
            'client_id': client_id,
            'username': username,
          }
        },
        'status': true
      };

    } on DioException catch (e) {
      // Manejar errores HTTP específicos
      if (e.response?.statusCode == 401) {
        logger.severe('Error de autenticación: ${e.message}');
        return {
          'code': 401,
          'message': 'authentication_error',
          'result': e.message ?? 'Error de autenticación',
          'status': false
        };
      } else if (e.type == DioExceptionType.connectionTimeout ||
                 e.type == DioExceptionType.receiveTimeout ||
                 e.type == DioExceptionType.connectionError) {
        logger.severe('Error de conexión: ${e.message}');
        return {
          'code': 503,
          'message': 'connection_error',
          'result': e.message ?? 'Error de conexión',
          'status': false
        };
      } else {
        logger.severe('Error de Keycloak: ${e.message}');
        return {
          'code': 400,
          'message': 'keycloak_error',
          'result': e.message ?? 'Error de Keycloak',
          'status': false
        };
      }
    } on KeycloakAuthenticationError catch (e) {
      logger.severe('Error de autenticación: $e');
      return {
        'code': 401,
        'message': 'authentication_error',
        'result': e.toString(),
        'status': false
      };
    } on KeycloakConnectionError catch (e) {
      logger.severe('Error de conexión: $e');
      return {
        'code': 503,
        'message': 'connection_error',
        'result': e.toString(),
        'status': false
      };
    } on KeycloakError catch (e) {
      logger.severe('Error de Keycloak: $e');
      return {
        'code': 400,
        'message': 'keycloak_error',
        'result': e.toString(),
        'status': false
      };
    } catch (e) {
      logger.severe('Error inesperado: $e');
      return {
        'code': 500,
        'message': 'internal_error',
        'result': e.toString(),
        'status': false
      };
    }
  }



  /// Valida un token de acceso existente
  ///
  /// Args:
  ///   server_url (String): URL del servidor Keycloak
  ///   realm (String): Nombre del realm
  ///   client_id (String): ID del cliente
  ///   access_token (String): Token de acceso a validar
  ///   client_secret (String, optional): Secret del cliente
  ///
  /// Returns:
  ///   Map<String, dynamic>: Respuesta con información de validación
  static Future<Map<String, dynamic>> validate_token({
    required String server_url,
    required String realm,
    required String client_id,
    required String access_token,
    String? client_secret,
  }) async {
    try {
      String normalizedUrl = _normalizeUrl(server_url);

      // Introspeccionar el token
      final tokenInfo = await _introspectToken(
        server_url: normalizedUrl,
        realm: realm,
        client_id: client_id,
        client_secret: client_secret,
        token: access_token,
      );

      if (!(tokenInfo['active'] as bool? ?? false)) {
        return {
          'code': 401,
          'message': 'token_invalid',
          'result': 'Token no está activo',
          'status': false
        };
      }

      // Obtener información del usuario si el token es válido
      Map<String, dynamic>? userInfo;
      try {
        userInfo = await _getUserInfo(
          server_url: normalizedUrl,
          realm: realm,
          access_token: access_token,
        );
      } catch (e) {
        userInfo = null;
      }

      return {
        'code': 200,
        'message': 'success',
        'result': {
          'token_info': tokenInfo,
          'user_info': userInfo,
          'is_valid': true
        },
        'status': true
      };

    } catch (e) {
      logger.severe('Error validando token: $e');
      return {
        'code': 500,
        'message': 'validation_error',
        'result': 'Error validando token: ${e.toString()}',
        'status': false
      };
    }
  }

  /// Endpoint de salud para verificar que el servicio está funcionando
  ///
  /// Returns:
  ///   Map<String, dynamic>: Respuesta con código, mensaje, resultado y status
  static Future<Map<String, dynamic>> health() async {
    try {
      // Obtener información del sistema
      final platform = Platform.operatingSystem;
      final architecture = Platform.numberOfProcessors.toString();
      final memory = ProcessInfo.currentRss;

      return {
        'code': 200,
        'message': 'success',
        'result': {
          'service_status': 'running',
          'system_info': {
            'platform': platform,
            'architecture': architecture,
            'memory': {
              'total': memory,
              'unit': 'bytes'
            }
          }
        },
        'status': true
      };

    } catch (e) {
      logger.severe('Error en endpoint de salud: $e');
      return {
        'code': 500,
        'message': 'health_check_error',
        'result': e.toString(),
        'status': false
      };
    }
  }
}

/// Función de conveniencia para mantener compatibilidad con código existente
Future<Map<String, dynamic>> keycloakLogin({
  required String server_url,
  required String realm,
  required String client_id,
  required String username,
  required String password,
  String? client_secret,
}) {
  return KeycloakService.keycloak_login(
    server_url: server_url,
    realm: realm,
    client_id: client_id,
    username: username,
    password: password,
    client_secret: client_secret,
  );
}