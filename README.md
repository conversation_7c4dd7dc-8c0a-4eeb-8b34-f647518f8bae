# Conexión a Keycloak API

Este proyecto implementa una serie de archivos que permiten gestionar la autenticación y autorización con *Keycloak*. Utiliza la biblioteca `python-keycloak` como eje principal de funcionamiento (para server en *Python*), para interactuar con el servidor de *Keycloak* de *Infositio*, proporcionando las herrameintas para login y validación de tokens.


# Dependencias

| Dependencia       | Versión   | Requerimiento                                 |
|-------------------|-----------|-----------------------------------------------|
| Python            | 3.10.0    | Obligatorio                                   |
| Docker            | 28.0.4    | Obligatorio                                   |
| python-keycloak   | 5.5.1     | Obligatorio                                   |
| Flask             | 3.1.0     | Opcional (usado para correr el test)          |
| PSutil            | 7.0.0     | Opcional (usado para el endpoint de salud)    |    


**Documentación oficial de la biblioteca** `python-keycloak` : [Documentación](https://python-keycloak.readthedocs.io/en/latest/)

## Instalación de dependencias

### Python

```bash
    pip install flask python-keycloak
    pip install psutil
```

### Dart

1. 


# Lenguajes Soportados

La aplicación actual soporta conexión a *Keycloak* para los siguientes lenguajes:

| Lenguaje  | Archivo                                           | Área en arquitectura |
|-----------|---------------------------------------------------|----------------------|
| Python    | `python_back_test/KeycloakController.py`          | Backend              |
| Dart      | `dart_test/lib/services/keycloak_service.dart`    | Backend              |

Todos estos archivos se encuentran adjuntos en este proyecto. La idea es tener un único archivo que pueda ser invocado, independiente del lenguaje que se use, y con llamar a algunos métodos, poder hacer todo el proceso de login.

## Casos de uso por Lenguaje

### Python

1. `python_back_test/test_api_back.py` : Inicializar este archivo permite ejecutar un server de *Python* para realizar peticiones, las cuales son siempre procesadas por el archivo principal `python_back_test/KeycloakController.py`.
2. `python_back_test/test_back.py` : Este archivo muestra la ejecución del login sin necesidad de un server de *Python*. Imprime los resultados **por consola**.

### Dart

1. `dart_test/lib/services/keycloak_service.dart` : Este archivo muestra la ejecución del login sin necesidad de un server de *Python*. Imprime los resultados **por consola**.

# Keycloak & Docker

*Keycloak* es una plataforma de gestión de identidad y acceso (*IAM*) que proporciona autenticación y autorización para aplicaciones web y servicios. Permite a las aplicaciones gestionar usuarios, roles y permisos de manera centralizada y segura.

Para el proyecto actual, se tomó en cuenta instalarlo usando las siguientes características:

- **Uso de contenedor** : Sí (Docker) 
- **Path actual del contenedor**: `/discodatos/keycloak/docker-compose.yml`
- **Contenedor Keycloak** : [Docker Container](https://keycloak-qa.infositio.dev/)
- **Usuario Maestro Keycloak** :
    - **Username** : `admin`
    - **Password** : `admin_password`

**IMPORTANTE** : El archivo `Levantar Container Keycloak.md` contiene el paso a paso de como se generó el contenedor actual.

## Archivo del contenedor

Este archivo *yaml* es el archivo usado para crear el contenedor con *Keycloak* (en servidor *Infodevdockers*).

```yaml 
    services:
    keycloak:
        image: quay.io/keycloak/keycloak:24.0.4
        container_name: keycloak
        ports:
        - "8086:8080"
        environment:
        KEYCLOAK_ADMIN: admin
        KEYCLOAK_ADMIN_PASSWORD: admin_password
        KC_DB: postgres
        KC_DB_URL: **************************************************************
        KC_DB_URL_PORT: 5432
        KC_DB_USERNAME: postgres
        KC_DB_PASSWORD: Ko131an4E6ov096T
        KC_HOSTNAME: keycloak-qa.infositio.dev
        KC_PROXY: edge
        volumes:
        - keycloak_data:/opt/keycloak/data
        command: start-dev
        networks:
        - cloudflared_cloudflared_network

    volumes:
    postgres_data:
    keycloak_data:

    networks:
    cloudflared_cloudflared_network:
        external: true
```

# Configuraciones en Keycloak

Esta sección contiene la información y data referente al uso de *Keycloak*, una vez levantando y operativo su contenedor respectivo.

## Datos Iniciales

- **Realm** : *master*
- **Client** : *test*
- **User** : *user-dart*
- **Password** : *1234*
- **Client Secret** : *v3cXtEUhsqRmWmyvxO9SO1hQrCkLYMQV*

# Peticiones

**IMPORTANTE** : Se debe tener un servidor de Python activo antes de hacer la petición. Para ests ejemplos, se usará la ruta por defect usada en el archivo `test_api_back.py`, la cuál es `http://localhost:5000`.

## Login

- **Descripción** : Permite loguearse en el sistema y obtener un token de acceso.
- **Tipo** : `POST`
- **URL** : `/login`
- **Body** : 
    ```json
        {
            "server_url": "https://url.com", // URL del servidor Keycloak
            "realm": "master",
            "client_id": "test",
            "username": "test",
            "password": "1234",
            "client_secret": "value_client_secret"
        }
    ```
- **Disponible para** : Python
- **Ejemplo con CURL** : 
    ```curl
    curl -X POST http://localhost:5000/login -H "Content-Type: application/json" -d '{"server_url": "https://keycloak-qa.infositio.dev/", "realm": "master", "client_id": "test", "username": "user-dart", "password": "1234", "client_secret": "v3cXtEUhsqRmWmyvxO9SO1hQrCkLYMQV"}'
    ```

- **Respuesta en caso de éxito** :

    ```json
    <truncated 3 lines>
    {
    "code": 200,
    "message": "success",
    "result": {
        "connection_info": {
        "client_id": "test",
        "realm": "master",
        "server_url": "https://keycloak-qa.infositio.dev/",
        "username": "user-dart"
        },
        "token": {
        "access_token": "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJkWmNPSklkWi1udUtSbDlDM2JFaVpXYkt1ak5JOVU4aFR2cGI1T3drZUY0In0.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.OLSTEWZf6tZEOd3jRnt3KzTX1hHhB8HwD4Hi1obvt266hdmub3Vj90xDKrtY00uRLYYD1X05hovRxkPlqq_rqKw8s3UHPRIGkWq8O5QduLanNy86TLYRd4-H-YhrHbHC8AMjujpIImupXVfl90j20y6IBxPhSAjkgZ_WXRlc7YBl6NW_wSQ8OkIjw7pKA-Io_EtDnXEhDFNK8sYmJDffHhfEPZA2BKLvcXDc7_JU-ISaJuTA88HOB5SBZlEZseqOnzTeWg4gcovL0Ta2-NLWmDptEaqQ_jUXhvgmpUhSvAyL4ehJoq7th8AYXiYKGyjpzi75mZXDR9JnM5_LQ-HsPg",
        "expires_in": 60,
        "id_token": "*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
        "not-before-policy": 0,
        "refresh_expires_in": 1800,
        "refresh_token": "eyJhbGciOiJIUzUxMiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICIxNjRmN2JkYi1lMjU5LTRjZTUtYmViOC0xYjNmOThlZjA1NGIifQ.eyJleHAiOjE3NDg4OTM0MzAsImlhdCI6MTc0ODg5MTYzMCwianRpIjoiYzYzYjdmMjctNTNjNS00NTZkLWJlMWYtMjA1NGQ3MDQ4ZDNmIiwiaXNzIjoiaHR0cHM6Ly9rZXljbG9hay1xYS5pbmZvc2l0aW8uZGV2L3JlYWxtcy9tYXN0ZXIiLCJhdWQiOiJodHRwczovL2tleWNsb2FrLXFhLmluZm9zaXRpby5kZXYvcmVhbG1zL21hc3RlciIsInN1YiI6IjFjMjUyNDVjLTY2YTMtNDU4My1iMTY1LWYzMDJlMGZjZDc2MiIsInR5cCI6IlJlZnJlc2giLCJhenAiOiJ0ZXN0Iiwic2Vzc2lvbl9zdGF0ZSI6ImE5NjdkYjQyLTgyYzktNGM5Zi05ZDE0LTM5YTk2MzE0OWQ1MSIsInNjb3BlIjoib3BlbmlkIHByb2ZpbGUgZW1haWwiLCJzaWQiOiJhOTY3ZGI0Mi04MmM5LTRjOWYtOWQxNC0zOWE5NjMxNDlkNTEifQ.nPjHhH6fdcQNOkS18f5o3HZ3DM1dZcpB0AVSRMlWMgq-7MDt3geYsV4S6g2ApoD-nYZj0lqVD4JmqS6dgR_p2A",
        "scope": "openid profile email",
        "session_state": "a967db42-82c9-4c9f-9d14-39a963149d51",
        "token_type": "Bearer"
        },
        "token_info": {
        "acr": "1",
        "active": true,
        "allowed-origins": [
            "http://localhost"
        ],
        "aud": "account",
        "azp": "test",
        "client_id": "test",
        "email_verified": false,
        "exp": **********,
        "family_name": "LastNameTest",
        "given_name": "NameTest",
        "iat": **********,
        "iss": "https://keycloak-qa.infositio.dev/realms/master",
        "jti": "efea79d5-9a0a-4774-88ea-223b84f6f514",
        "name": "NameTest LastNameTest",
        "preferred_username": "user-dart",
        "realm_access": {
            "roles": [
            "default-roles-master",
            "offline_access",
            "uma_authorization"
            ]
        },
        "resource_access": {
            "account": {
            "roles": [
                "manage-account",
                "manage-account-links",
                "view-profile"
            ]
            }
        },
        "scope": "openid profile email",
        "session_state": "a967db42-82c9-4c9f-9d14-39a963149d51",
        "sid": "a967db42-82c9-4c9f-9d14-39a963149d51",
        "sub": "1c25245c-66a3-4583-b165-f302e0fcd762",
        "token_type": "Bearer",
        "typ": "Bearer",
        "username": "user-dart"
        },
        "user_info": {
        "email_verified": false,
        "family_name": "LastNameTest",
        "given_name": "NameTest",
        "name": "NameTest LastNameTest",
        "preferred_username": "user-dart",
        "sub": "1c25245c-66a3-4583-b165-f302e0fcd762"
        }
    },
    "status": true
    }
    Exit Code 0
    ```


## Validate Token

- **Descripción** : Valida un token de acceso.
- **Tipo** : `POST`
- **URL** : `/validate-token`
- **Body** :
```json
{
    "server_url": "https://tu-keycloak.com/",
    "realm": "tu-realm",
    "client_id": "tu-client-id",
    "access_token": "token-a-validar",
    "client_secret": "secret" (opcional, solo para clientes confidenciales)
}
```
- **Disponible para** : Python
- **Ejemplo con CURL** : 
```curl
curl -X POST http://localhost:5000/validate-token -H "Content-Type: application/json" -d '{"server_url": "https://keycloak-qa.infositio.dev/", "realm": "master", "client_id": "test", "access_token": "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJkWmNPSklkWi1udUtSbDlDM2JFaVpXYkt1ak5JOVU4aFR2cGI1T3drZUY0In0.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.C--JQ-s_OuIexn-gS2mZ6FxaHR_21_izl3onOa_-QbkGSLnmuqdpgW-qqQxMrSLPCkAZNmfI3YWpnoEszwu80iTi0zYGepZja1aibIHfj8pCr2ST8KBTXl9oZr0vCkOMTsVRAwuwN5XXH1riKrbqe7ORaJ3ElzzPG37mjPW-44GpIv_gFefPB9dPqi1W5Ta8_wQY8gbOoazHqHkrC0YOTsoxYtV8QwwJ_tbYuFTjFXsRgwV3cOtQHHfz9ifRi-d__DBK7FEs7x1QCpFeoiS8Vn_75m_ErVgx8IdAld3F7iHaIrTrZI09HcQxsaScTahKvoPvoS7Q36T_FdOO-LlGCw", "client_secret": "v3cXtEUhsqRmWmyvxO9SO1hQrCkLYMQV"}'
```

## Health

- **Descripción** : Retorna información sobre el estado del servicio.
- **Tipo** : `GET`
- **URL** : `/health`
- **Ejemplo con CURL** : 
```curl
curl -X GET http://localhost:5000/health
```


# Comandos Útiles

## Docker

- **Log en tiempo real del contenedor Keycloak** : `docker logs -f keycloak`
- **Entrar al contenedor Keycloak** : `docker exec -it keycloak /bin/bash`

## Keycloak

- **Autentificarse en Keycloak** : 
    - Este paso **es obligatorio** para poder usar los comandos de *Keycloak* y debe ejecutarse antes de cualquier otro comando.
    - `/opt/keycloak/bin/kcadm.sh config credentials --server http://localhost:8080 --realm master --user admin --password tu_contraseña`
    - Se debe especificar en el comando: **realm**, **user** y **password**.

- **Crear un Client (confidential)** : 
```
/opt/keycloak/bin/kcadm.sh create clients -r master \
  -s clientId=test \
  -s name=test \
  -s enabled=true \
  -s protocol=openid-connect \
  -s publicClient=false \
  -s clientAuthenticatorType=client-secret \
  -s 'redirectUris=["http://localhost/*"]'
```

- **Obtener ID de Cliente**:
```
/opt/keycloak/bin/kcadm.sh get clients -r master --fields id,clientId | grep -B 1 '"clientId" : "test"'
```

- **Obtener client_secret**:
    - El valor de `abc1234-xxxx-xxxx-xxxx-xxxxxxxxxxxx` debe ser reemplazado por el ID obtenido en el paso anterior.
    - **Comando** : `/opt/keycloak/bin/kcadm.sh get clients/abc1234-xxxx-xxxx-xxxx-xxxxxxxxxxxx/client-secret -r master`

- **Verificar configuración del cliente**:
    - El valor de `abc1234-xxxx-xxxx-xxxx-xxxxxxxxxxxx` debe ser reemplazado por el ID obtenido en el paso anterior.
    - **Comando** : `/opt/keycloak/bin/kcadm.sh get clients/abc1234-xxxx-xxxx-xxxx-xxxxxxxxxxxx -r qa-realm`

## Flutter

- **Listar dispositivos conectados** : `flutter devices`
- **Listar emuladores disponibles** : `flutter emulators`
- **Iniciar emulador** : `flutter emulators --launch <emulator_id>`
- **Correr app en emulador** : `flutter run`
- **Limpiar cache de Flutter** : `flutter clean`
